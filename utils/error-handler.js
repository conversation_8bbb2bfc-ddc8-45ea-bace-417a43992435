// 错误处理和日志系统
const ErrorHandler = {
  // 错误类型定义
  ErrorTypes: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    API_ERROR: 'API_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    PERMISSION_ERROR: 'PERMISSION_ERROR',
    SYSTEM_ERROR: 'SYSTEM_ERROR',
    USER_ERROR: 'USER_ERROR'
  },
  
  // 错误级别
  ErrorLevels: {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    FATAL: 4
  },
  
  // 日志存储
  logs: [],
  maxLogs: 1000, // 最大日志数量
  
  // 错误处理配置
  config: {
    enableConsoleLog: true,
    enableRemoteLog: false,
    enableUserFeedback: true,
    maxRetries: 3,
    retryDelay: 1000
  },
  
  // 创建错误对象
  createError: (type, message, details = {}) => {
    return {
      type,
      message,
      details,
      timestamp: new Date().toISOString(),
      stack: new Error().stack,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'Unknown'
    };
  },
  
  // 记录日志
  log: (level, message, data = {}) => {
    const logEntry = {
      id: ErrorHandler.generateId(),
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      source: 'client'
    };
    
    // 添加到本地日志
    ErrorHandler.logs.push(logEntry);
    
    // 限制日志数量
    if (ErrorHandler.logs.length > ErrorHandler.maxLogs) {
      ErrorHandler.logs.shift();
    }
    
    // 控制台输出
    if (ErrorHandler.config.enableConsoleLog) {
      ErrorHandler.consoleLog(level, message, data);
    }
    
    // 远程日志上报
    if (ErrorHandler.config.enableRemoteLog) {
      ErrorHandler.reportToRemote(logEntry);
    }
    
    return logEntry;
  },
  
  // 控制台日志输出
  consoleLog: (level, message, data) => {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}]`;
    
    switch (level) {
      case ErrorHandler.ErrorLevels.DEBUG:
        console.debug(prefix, message, data);
        break;
      case ErrorHandler.ErrorLevels.INFO:
        console.info(prefix, message, data);
        break;
      case ErrorHandler.ErrorLevels.WARN:
        console.warn(prefix, message, data);
        break;
      case ErrorHandler.ErrorLevels.ERROR:
        console.error(prefix, message, data);
        break;
      case ErrorHandler.ErrorLevels.FATAL:
        console.error(prefix, '🚨 FATAL:', message, data);
        break;
      default:
        console.log(prefix, message, data);
    }
  },
  
  // 处理错误
  handleError: (error, context = {}) => {
    let errorObj;
    
    // 标准化错误对象
    if (typeof error === 'string') {
      errorObj = ErrorHandler.createError(ErrorHandler.ErrorTypes.SYSTEM_ERROR, error, context);
    } else if (error instanceof Error) {
      errorObj = ErrorHandler.createError(
        ErrorHandler.ErrorTypes.SYSTEM_ERROR,
        error.message,
        { ...context, stack: error.stack }
      );
    } else {
      errorObj = error;
    }
    
    // 记录错误日志
    ErrorHandler.log(ErrorHandler.ErrorLevels.ERROR, errorObj.message, errorObj);
    
    // 根据错误类型处理
    switch (errorObj.type) {
      case ErrorHandler.ErrorTypes.NETWORK_ERROR:
        return ErrorHandler.handleNetworkError(errorObj);
      case ErrorHandler.ErrorTypes.API_ERROR:
        return ErrorHandler.handleApiError(errorObj);
      case ErrorHandler.ErrorTypes.VALIDATION_ERROR:
        return ErrorHandler.handleValidationError(errorObj);
      case ErrorHandler.ErrorTypes.PERMISSION_ERROR:
        return ErrorHandler.handlePermissionError(errorObj);
      default:
        return ErrorHandler.handleGenericError(errorObj);
    }
  },
  
  // 处理网络错误
  handleNetworkError: (error) => {
    const userMessage = '网络连接异常，请检查网络设置后重试';
    
    if (ErrorHandler.config.enableUserFeedback) {
      ErrorHandler.showUserMessage(userMessage, 'error');
    }
    
    return {
      handled: true,
      userMessage,
      shouldRetry: true,
      retryDelay: ErrorHandler.config.retryDelay
    };
  },
  
  // 处理API错误
  handleApiError: (error) => {
    const { details } = error;
    let userMessage = '服务暂时不可用，请稍后重试';
    
    // 根据API错误码定制消息
    if (details.code) {
      switch (details.code) {
        case 1001:
          userMessage = '请求参数错误';
          break;
        case 1002:
          userMessage = '数据不存在';
          break;
        case 1003:
          userMessage = '权限不足';
          break;
        case 3001:
          userMessage = '操作失败，请重试';
          break;
        case 5001:
          userMessage = '系统繁忙，请稍后重试';
          break;
      }
    }
    
    if (ErrorHandler.config.enableUserFeedback) {
      ErrorHandler.showUserMessage(userMessage, 'error');
    }
    
    return {
      handled: true,
      userMessage,
      shouldRetry: details.code >= 5000, // 5000以上的错误码可以重试
      retryDelay: ErrorHandler.config.retryDelay
    };
  },
  
  // 处理验证错误
  handleValidationError: (error) => {
    const userMessage = error.message || '输入信息有误，请检查后重试';
    
    if (ErrorHandler.config.enableUserFeedback) {
      ErrorHandler.showUserMessage(userMessage, 'warning');
    }
    
    return {
      handled: true,
      userMessage,
      shouldRetry: false
    };
  },
  
  // 处理权限错误
  handlePermissionError: (error) => {
    const userMessage = '权限不足，请重新登录';
    
    if (ErrorHandler.config.enableUserFeedback) {
      ErrorHandler.showUserMessage(userMessage, 'error');
    }
    
    // 可能需要重新登录
    ErrorHandler.handleAuthRequired();
    
    return {
      handled: true,
      userMessage,
      shouldRetry: false,
      requiresAuth: true
    };
  },
  
  // 处理通用错误
  handleGenericError: (error) => {
    const userMessage = '操作失败，请重试';
    
    if (ErrorHandler.config.enableUserFeedback) {
      ErrorHandler.showUserMessage(userMessage, 'error');
    }
    
    return {
      handled: true,
      userMessage,
      shouldRetry: true,
      retryDelay: ErrorHandler.config.retryDelay
    };
  },
  
  // 显示用户消息
  showUserMessage: (message, type = 'info') => {
    if (typeof wx !== 'undefined') {
      switch (type) {
        case 'success':
          wx.showToast({
            title: message,
            icon: 'success',
            duration: 2000
          });
          break;
        case 'error':
        case 'warning':
          wx.showToast({
            title: message,
            icon: 'none',
            duration: 3000
          });
          break;
        default:
          wx.showToast({
            title: message,
            icon: 'none',
            duration: 2000
          });
      }
    } else {
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
  },
  
  // 处理需要认证的情况
  handleAuthRequired: () => {
    // 清除本地认证信息
    if (typeof wx !== 'undefined') {
      try {
        wx.removeStorageSync('userToken');
        wx.removeStorageSync('userInfo');
      } catch (e) {
        console.warn('清除本地存储失败:', e);
      }
    }
    
    // 跳转到登录页面或显示登录弹窗
    ErrorHandler.log(ErrorHandler.ErrorLevels.WARN, '用户需要重新认证');
  },
  
  // 远程日志上报
  reportToRemote: async (logEntry) => {
    try {
      // 这里应该调用远程日志服务
      // 为了避免循环错误，使用简单的网络请求
      if (typeof wx !== 'undefined') {
        wx.request({
          url: 'https://your-log-service.com/api/logs',
          method: 'POST',
          data: logEntry,
          success: () => {
            // 日志上报成功
          },
          fail: (error) => {
            // 日志上报失败，但不要再次触发错误处理
            console.warn('日志上报失败:', error);
          }
        });
      }
    } catch (error) {
      console.warn('日志上报异常:', error);
    }
  },
  
  // 获取日志
  getLogs: (level = null, limit = 100) => {
    let filteredLogs = ErrorHandler.logs;
    
    if (level !== null) {
      filteredLogs = ErrorHandler.logs.filter(log => log.level >= level);
    }
    
    return filteredLogs.slice(-limit);
  },
  
  // 清空日志
  clearLogs: () => {
    ErrorHandler.logs = [];
  },
  
  // 导出日志
  exportLogs: () => {
    const logsData = {
      logs: ErrorHandler.logs,
      exportTime: new Date().toISOString(),
      version: '1.0'
    };
    
    return JSON.stringify(logsData, null, 2);
  },
  
  // 生成唯一ID
  generateId: () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },
  
  // 设置配置
  setConfig: (newConfig) => {
    ErrorHandler.config = { ...ErrorHandler.config, ...newConfig };
  },
  
  // 便捷方法
  debug: (message, data) => ErrorHandler.log(ErrorHandler.ErrorLevels.DEBUG, message, data),
  info: (message, data) => ErrorHandler.log(ErrorHandler.ErrorLevels.INFO, message, data),
  warn: (message, data) => ErrorHandler.log(ErrorHandler.ErrorLevels.WARN, message, data),
  error: (message, data) => ErrorHandler.log(ErrorHandler.ErrorLevels.ERROR, message, data),
  fatal: (message, data) => ErrorHandler.log(ErrorHandler.ErrorLevels.FATAL, message, data)
};

// 全局错误捕获
if (typeof wx !== 'undefined') {
  // 小程序错误监听
  wx.onError((error) => {
    ErrorHandler.handleError(error, { source: 'wx.onError' });
  });
  
  // 未处理的Promise拒绝
  wx.onUnhandledRejection((res) => {
    ErrorHandler.handleError(res.reason, { source: 'unhandledRejection' });
  });
}

// 导出
module.exports = ErrorHandler;
