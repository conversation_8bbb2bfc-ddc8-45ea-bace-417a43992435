# 心安AI - 焦虑管理小程序

## 项目简介

"心安AI"是一款专注于缓解焦虑情绪的AI心理支持微信小程序。通过智能化的引导，帮助用户将压得喘不过气的"一团乱麻"梳理成清晰、可控的行动路径，让他们通过一步步的行动重获对生活的掌控感，实现"在行动中消解焦虑"的目标。

## 核心功能

### 1. 焦虑倾诉 (Anxiety Dump)
- 提供安全、私密的环境让用户表达焦虑
- 支持文字和语音双重输入方式
- 智能引导和提示功能
- 自动草稿保存

### 2. AI智能拆解 (AI Task Breakdown)
- 使用AI分析用户的焦虑内容
- 识别情绪关键词和焦虑源
- 将模糊焦虑拆解为具体可执行任务
- 智能设定优先级和时间建议

### 3. 任务清单管理 (Actionable Checklist)
- 清晰展示AI生成的任务列表
- 支持任务编辑、完成、删除操作
- 任务状态管理和进度跟踪
- 即时反馈和成就感

### 4. 订阅式提醒 (Subscribe Message)
- 基于微信订阅消息的提醒功能
- 智能推送任务开始提醒
- 用户可自定义提醒设置

### 5. 情绪追踪日记 (Mood Journal)
- 简单快捷的情绪记录
- 情绪变化趋势分析
- 可视化情绪数据展示

### 6. 成就分享卡片 (Victory Card Sharing)
- 完成任务后自动生成成就卡片
- 支持分享到微信好友或朋友圈
- 正向激励和成就感强化

## 技术架构

### 前端技术
- **框架**: 微信小程序原生框架
- **样式**: 自定义CSS设计系统
- **状态管理**: 小程序原生数据绑定
- **组件化**: 可复用组件设计

### 后端技术
- **云服务**: 微信云开发 (Serverless)
- **数据库**: 云数据库 (MongoDB)
- **云函数**: Node.js
- **文件存储**: 云存储

### AI服务
- **自然语言处理**: 集成第三方AI API
- **语音识别**: 微信同声传译API
- **情绪分析**: 自定义算法 + AI模型

## 项目结构

```
xinan/
├── pages/                    # 页面文件
│   ├── index/               # 首页
│   ├── anxiety-dump/        # 焦虑倾诉页
│   ├── analysis-result/     # 分析结果页
│   ├── task-list/           # 任务列表页
│   ├── task-detail/         # 任务详情页
│   ├── mood-journal/        # 情绪日记页
│   ├── profile/             # 个人中心页
│   └── auth/                # 授权页面
├── components/              # 公共组件
├── utils/                   # 工具函数
├── images/                  # 图片资源
├── cloudfunctions/          # 云函数
│   ├── auth/               # 用户认证
│   ├── anxiety/            # 焦虑倾诉
│   ├── tasks/              # 任务管理
│   ├── mood/               # 情绪日记
│   ├── notification/       # 消息推送
│   └── achievements/       # 成就系统
├── design/                  # 设计文档
├── doc/                     # 项目文档
├── app.js                   # 小程序入口
├── app.json                 # 小程序配置
├── app.wxss                 # 全局样式
└── project.config.json      # 项目配置
```

## 开发环境搭建

### 1. 环境要求
- 微信开发者工具 (最新版本)
- Node.js 14.0+
- 微信小程序账号
- 微信云开发环境

### 2. 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/xinan-ai.git
cd xinan-ai
```

2. **配置微信开发者工具**
- 打开微信开发者工具
- 导入项目，选择项目目录
- 配置AppID (在project.config.json中)

3. **配置云开发环境**
- 在微信开发者工具中开通云开发
- 创建云开发环境
- 部署云函数

4. **启动项目**
- 在微信开发者工具中点击"编译"
- 在模拟器中预览效果

### 3. 云函数部署

```bash
# 部署所有云函数
npm run deploy:all

# 或单独部署
npm run deploy:auth
npm run deploy:anxiety
npm run deploy:tasks
```

## 使用说明

### 用户流程
1. **微信授权登录** - 获取用户基本信息
2. **倾诉焦虑** - 通过文字或语音表达困扰
3. **AI分析** - 系统智能分析并生成任务建议
4. **任务管理** - 查看、编辑、完成AI生成的任务
5. **情绪记录** - 记录每日情绪变化
6. **成就分享** - 完成任务后获得成就卡片

### 主要页面
- **首页**: 快速操作入口和数据统计
- **焦虑倾诉**: 安全的情绪表达空间
- **分析结果**: AI分析结果和任务建议
- **任务列表**: 任务管理和状态跟踪
- **情绪日记**: 情绪记录和趋势分析
- **个人中心**: 用户设置和数据统计

## 设计理念

### 用户体验
- **温暖治愈**: 柔和的色彩和友好的交互
- **简约直观**: 减少认知负担，专注核心功能
- **隐私安全**: 数据加密存储，用户隐私保护
- **即时反馈**: 及时的操作反馈和成就感

### 技术特色
- **AI驱动**: 智能分析和个性化建议
- **微信生态**: 充分利用微信小程序特性
- **云原生**: Serverless架构，自动扩容
- **数据驱动**: 基于用户行为的持续优化

## 开发计划

### Phase 1: 核心功能 (已完成)
- ✅ 项目初始化和基础架构
- ✅ 用户认证系统
- ✅ 焦虑倾诉功能
- ✅ AI智能拆解功能
- ✅ 任务清单管理

### Phase 2: 辅助功能 (开发中)
- 🔄 订阅式提醒功能
- 🔄 情绪追踪日记功能
- 🔄 用户数据统计

### Phase 3: 增值功能 (计划中)
- ⏳ 成就分享卡片功能
- ⏳ 性能优化
- ⏳ 用户体验优化

### Phase 4: 测试上线 (计划中)
- ⏳ 功能测试
- ⏳ 性能测试
- ⏳ 安全测试
- ⏳ 上线部署

## 贡献指南

### 代码规范
- 使用ESLint进行代码检查
- 遵循微信小程序开发规范
- 组件化开发，提高代码复用性
- 详细的注释和文档

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

- **项目地址**: https://github.com/your-repo/xinan-ai
- **问题反馈**: https://github.com/your-repo/xinan-ai/issues
- **邮箱**: <EMAIL>

## 致谢

感谢所有为心理健康事业做出贡献的开发者和研究者。让我们一起用技术的力量，帮助更多人找到内心的平静。
