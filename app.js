// app.js
App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    openid: null,
    systemInfo: null
  },

  onLaunch() {
    console.log('心安AI小程序启动');
    
    // 初始化云开发
    this.initCloud();
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 检查更新
    this.checkForUpdate();
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(msg) {
    console.error('小程序错误:', msg);
    // 可以在这里上报错误信息
  },

  // 初始化云开发
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    wx.cloud.init({
      env: 'xinan-ai-prod', // 云开发环境ID
      traceUser: true
    });

    console.log('云开发初始化成功');
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      },
      fail: (err) => {
        console.error('获取系统信息失败:', err);
      }
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const openid = wx.getStorageSync('openid');
    
    if (userInfo && openid) {
      this.globalData.userInfo = userInfo;
      this.globalData.openid = openid;
      this.globalData.isLoggedIn = true;
      console.log('用户已登录:', userInfo);
    } else {
      console.log('用户未登录');
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate);
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 调用云函数进行登录
            wx.cloud.callFunction({
              name: 'auth',
              data: {
                action: 'login',
                code: res.code
              },
              success: (cloudRes) => {
                if (cloudRes.result.code === 0) {
                  const { openid, userInfo } = cloudRes.result.data;
                  
                  // 保存用户信息
                  this.globalData.openid = openid;
                  this.globalData.userInfo = userInfo;
                  this.globalData.isLoggedIn = true;
                  
                  // 本地存储
                  wx.setStorageSync('openid', openid);
                  wx.setStorageSync('userInfo', userInfo);
                  
                  console.log('登录成功:', userInfo);
                  resolve(cloudRes.result.data);
                } else {
                  console.error('登录失败:', cloudRes.result.message);
                  reject(new Error(cloudRes.result.message));
                }
              },
              fail: (err) => {
                console.error('调用登录云函数失败:', err);
                reject(err);
              }
            });
          } else {
            console.error('获取登录凭证失败:', res.errMsg);
            reject(new Error(res.errMsg));
          }
        },
        fail: (err) => {
          console.error('wx.login失败:', err);
          reject(err);
        }
      });
    });
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          const userInfo = res.userInfo;
          
          // 更新用户信息到云端
          wx.cloud.callFunction({
            name: 'auth',
            data: {
              action: 'updateProfile',
              userInfo: userInfo
            },
            success: (cloudRes) => {
              if (cloudRes.result.code === 0) {
                // 更新本地用户信息
                this.globalData.userInfo = {
                  ...this.globalData.userInfo,
                  ...userInfo
                };
                
                wx.setStorageSync('userInfo', this.globalData.userInfo);
                console.log('用户信息更新成功');
                resolve(this.globalData.userInfo);
              } else {
                reject(new Error(cloudRes.result.message));
              }
            },
            fail: reject
          });
        },
        fail: reject
      });
    });
  },

  // 退出登录
  logout() {
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    this.globalData.openid = null;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('openid');
    wx.removeStorageSync('anxiety_draft');
    
    console.log('用户已退出登录');
  },

  // 显示错误提示
  showError(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: duration
    });
  },

  // 显示成功提示
  showSuccess(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: duration
    });
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');
    const second = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  },

  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
});
